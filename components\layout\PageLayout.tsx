'use client';

import { ReactNode } from 'react';
import Enhanced<PERSON><PERSON>bar from './EnhancedNavbar';
import EnhancedFooter from './EnhancedFooter';
import { motion } from 'framer-motion';

interface PageLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
  keywords?: string;
  customStyles?: string;
}

const PageLayout = ({ 
  children, 
  title = 'NextGen Youth Movement',
  description = 'Empowering tomorrow\'s leaders through faith-based youth development, mentorship, and community engagement.',
  keywords = 'youth, leadership, mentorship, community, faith, development, next generation',
  customStyles = ''
}: PageLayoutProps) => {
  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    in: { opacity: 1, y: 0 },
    out: { opacity: 0, y: -20 }
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.5
  };

  return (
    <>
      <div className={`min-h-screen flex flex-col ${customStyles}`}>
        <EnhancedNavbar />
        
        <motion.main
          initial="initial"
          animate="in"
          exit="out"
          variants={pageVariants}
          transition={pageTransition}
          className="flex-grow pt-16"
        >
          {children}
        </motion.main>
        
        <EnhancedFooter />
      </div>
    </>
  );
};

export default PageLayout;
