'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, useInView, useAnimation } from 'framer-motion';

interface StatItem {
  number: number;
  suffix: string;
  label: string;
  description: string;
  color: string;
  gradient: string;
}

interface CircularProgressProps {
  stat: StatItem;
  index: number;
  isVisible: boolean;
}

const CircularProgress = ({ stat, index, isVisible }: CircularProgressProps) => {
  const [count, setCount] = useState(0);
  const [progress, setProgress] = useState(0);
  const controls = useAnimation();
  const ref = useRef(null);
  const inView = useInView(ref, { once: true });

  useEffect(() => {
    if (inView && isVisible) {
      // Synchronize both animations to start at the same time
      const duration = 2000; // 2 seconds
      const startTime = Date.now() + (index * 200); // Stagger the start
      
      const animate = () => {
        const now = Date.now();
        const elapsed = now - startTime;
        
        if (elapsed < 0) {
          requestAnimationFrame(animate);
          return;
        }
        
        const progressValue = Math.min(elapsed / duration, 1);
        const easeOutProgress = 1 - Math.pow(1 - progressValue, 3); // Ease out cubic
        
        
        // Update both counter and progress simultaneously
        setCount(Math.floor(stat.number * easeOutProgress));
        setProgress(easeOutProgress);
        
        if (progressValue < 1) {
          requestAnimationFrame(animate);
        } else {
          setCount(stat.number);
          setProgress(1);
        }
      };
      
      requestAnimationFrame(animate);
    }
  }, [inView, isVisible, stat.number, index, controls]);

  const circumference = 2 * Math.PI * 45; // radius = 45
  const strokeDashoffset = circumference * (1 - progress * 0.75); // 75% max fill

  return (
    <div ref={ref} className="relative">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={inView ? { scale: 1, opacity: 1 } : {}}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        className="relative"
      >
        {/* Circular Progress SVG */}
        <div className="relative w-32 h-32 mx-auto">
          <svg
            className="w-full h-full transform -rotate-90"
            viewBox="0 0 100 100"
          >
            {/* Background Circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="currentColor"
              strokeWidth="6"
              fill="none"
              className="text-muted/20"
            />
            
            {/* Progress Circle */}
            <motion.circle
              cx="50"
              cy="50"
              r="45"
              stroke={`rgb(var(--${stat.color}))`}
              strokeWidth="6"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              className={`text-${stat.color} drop-shadow-lg`}
              style={{
                filter: `drop-shadow(0 0 8px rgb(var(--${stat.color}) / 0.5))`
              }}
              animate={controls}
              initial={{ strokeDashoffset: circumference }}
            />
          </svg>
          
          {/* Center Content */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <motion.div
                className={`text-3xl font-bold text-${stat.color} font-arvo`}
                initial={{ scale: 0 }}
                animate={inView ? { scale: 1 } : {}}
                transition={{ duration: 0.5, delay: index * 0.2 + 0.5 }}
              >
                {count}{stat.suffix}
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

const CircularProgressStats = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  const stats: StatItem[] = [
    {
      number: 500,
      suffix: '+',
      label: 'Youth Impacted',
      description: 'Lives transformed through our programs',
      color: 'electric-blue',
      gradient: 'from-electric-blue to-cyan-400'
    },
    {
      number: 12,
      suffix: '',
      label: 'Active Programs',
      description: 'Leadership and development initiatives',
      color: 'neon-green',
      gradient: 'from-neon-green to-green-400'
    },
    {
      number: 50,
      suffix: '+',
      label: 'Mentors',
      description: 'Dedicated leaders guiding youth',
      color: 'bright-orange',
      gradient: 'from-bright-orange to-orange-400'
    },
    {
      number: 8,
      suffix: '',
      label: 'Communities',
      description: 'Local areas we actively serve',
      color: 'hot-pink',
      gradient: 'from-hot-pink to-pink-400'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  return (
    <section id="stats" ref={sectionRef} className="py-20 stats-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-arvo font-bold text-foreground mb-6">
            Our <span className="bg-gradient-to-r from-electric-blue via-neon-green to-bright-orange bg-clip-text text-transparent">Impact</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Real numbers, real transformation. See how NextGen Youth Movement is making a difference 
            in communities across the region.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`stats-card-enhanced card-hover-effect card-${stat.color} rounded-xl p-8 text-center hover:scale-105 transition-all duration-300 group cursor-pointer relative overflow-hidden`}
            >
              {/* Animated background effect */}
              <div className={`absolute inset-0 bg-gradient-to-r ${stat.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
              
              {/* Circular Progress */}
              <div className="relative mb-6">
                <CircularProgress stat={stat} index={index} isVisible={isVisible} />
              </div>

              <div className="text-center relative z-10">
                <motion.h3
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                  className="text-xl font-arvo font-bold text-foreground mb-2 group-hover:text-foreground transition-colors duration-300"
                >
                  {stat.label}
                </motion.h3>
                <motion.p
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.4 }}
                  className="text-muted-foreground text-sm group-hover:text-muted-foreground transition-colors duration-300"
                >
                  {stat.description}
                </motion.p>
              </div>

              {/* Hover ripple effect */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-${stat.color} rounded-full animate-ping`}></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center mt-16"
        >
          <a
            href="#programs"
            className="inline-flex items-center space-x-2 px-8 py-4 bg-gradient-to-r from-electric-blue to-bright-orange text-white font-semibold rounded-lg hover:scale-105 transition-transform duration-300 font-arvo shadow-lg hover:shadow-xl"
          >
            <span>Be Part of the Next Success Story</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default CircularProgressStats;
